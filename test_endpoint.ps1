# Test the /todays-feed endpoint
$jsonContent = Get-Content "todays feed.json" -Raw
$uri = "http://localhost:8000/todays-feed"

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -Body $jsonContent -ContentType "application/json"
    Write-Host "✅ SUCCESS! Response received:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ ERROR! Status code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try to get the response content
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Yellow
    }
}
