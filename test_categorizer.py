#!/usr/bin/env python3
"""
Test script to verify the modified feed categorizer works correctly.
"""
import json
import asyncio
from app.utils.feed_categorizer import categorize_feed_posts, FeedPostItem

async def test_categorizer():
    # Load test data
    with open("todays feed.json", "r", encoding="utf-8") as f:
        test_data = json.load(f)
    
    # Convert to FeedPostItem objects
    posts = []
    for post_data in test_data["posts"]:
        post = FeedPostItem(
            activity_urn=post_data["activity_urn"],
            text=post_data["text"],
            total_reactions=post_data["total_reactions"],
            total_comments=post_data["total_comments"],
            total_shares=post_data["total_shares"],
            author_urn=post_data["author_urn"]
        )
        posts.append(post)
    
    print(f"🧪 Testing feed categorizer with {len(posts)} posts...")
    print(f"General persona keywords: {test_data['general_persona_keywords']}")
    
    try:
        # Test the categorizer
        result = await categorize_feed_posts(
            posts=posts,
            general_persona_keywords=test_data["general_persona_keywords"],
            content_persona_keywords=test_data.get("content_persona_keywords"),
            network_persona_keywords=test_data.get("network_persona_keywords")
        )
        
        print(f"✅ SUCCESS! Categorization completed")
        print(f"Number of categories: {len(result)}")
        
        total_posts_in_result = 0
        for category in result:
            category_posts = sum(len(subcat.get("posts", [])) for subcat in category.get("sub_categories", []))
            total_posts_in_result += category_posts
            print(f"  - {category.get('category_name', 'Unknown')}: {category_posts} posts")
        
        print(f"\nTotal posts in result: {total_posts_in_result}")
        print(f"Original posts: {len(posts)}")
        
        if total_posts_in_result == len(posts):
            print("✅ All posts are accounted for!")
        else:
            print(f"❌ Missing {len(posts) - total_posts_in_result} posts!")
        
        # Check minimum posts per category
        categories_with_min_posts = 0
        for category in result:
            category_posts = sum(len(subcat.get("posts", [])) for subcat in category.get("sub_categories", []))
            if category_posts >= 5:
                categories_with_min_posts += 1
        
        print(f"Categories with at least 5 posts: {categories_with_min_posts}/{len(result)}")
        
        return result
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_categorizer())
